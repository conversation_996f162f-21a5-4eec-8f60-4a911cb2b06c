import Web3 from "web3";
import { ethers } from "ethers";
import { ConfigService } from "@nestjs/config";
import { Injectable } from "@nestjs/common";

@Injectable()
export class Web3Instance {
  private RPC: any;
  private static web3Instance: Web3 | null = null;
  private static etherInstance: ethers.JsonRpcProvider | null = null;
  private static instanceCount = 0;
  private static readonly MAX_INSTANCES = 1; // Singleton pattern for workers

  constructor(private configService: ConfigService) {
    this.RPC = this.configService.get("MAINNET_RPC");
  }

  getWeb3Instance(): Web3 {
    // Use singleton pattern to prevent multiple instances
    if (!Web3Instance.web3Instance) {
      Web3Instance.web3Instance = new Web3(this.RPC);
      Web3Instance.instanceCount++;
      console.log(`Created Web3 instance #${Web3Instance.instanceCount}`);
    }
    return Web3Instance.web3Instance;
  }

  getEtherInstance(): ethers.JsonRpcProvider {
    // Use singleton pattern to prevent multiple instances
    if (!Web3Instance.etherInstance) {
      Web3Instance.etherInstance = new ethers.JsonRpcProvider(this.RPC, undefined, {
        // Add connection limits to prevent memory leaks
        batchMaxCount: 100,
        batchMaxSize: 1024 * 1024, // 1MB
        batchStallTime: 10, // 10ms
      });
      console.log(`Created Ethers instance #${Web3Instance.instanceCount}`);
    }
    return Web3Instance.etherInstance;
  }

  getRPC(): string {
    return this.RPC;
  }

  // Method to safely cleanup instances (use with caution)
  static cleanup(): void {
    try {
      if (Web3Instance.etherInstance) {
        // Ethers cleanup
        Web3Instance.etherInstance.destroy();
        Web3Instance.etherInstance = null;
      }

      if (Web3Instance.web3Instance) {
        // Web3 cleanup - be careful with this in worker threads
        try {
          const provider = Web3Instance.web3Instance.currentProvider;
          if (provider && typeof provider === 'object' && 'disconnect' in provider) {
            (provider as any).disconnect();
          }
        } catch (error) {
          console.warn('Error disconnecting Web3 provider:', error);
        }
        Web3Instance.web3Instance = null;
      }

      console.log('Web3Instance cleanup completed');
    } catch (error) {
      console.error('Error during Web3Instance cleanup:', error);
    }
  }

  // Get instance count for monitoring
  static getInstanceCount(): number {
    return Web3Instance.instanceCount;
  }
}
